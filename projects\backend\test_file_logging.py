#!/usr/bin/env python3
"""
Test script to verify file logging configuration:
- All logs (except DEBUG) written to files
- DEBUG logs only in console
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django with DEBUG=True to enable debug logs
os.environ["DEBUG"] = "True"
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agritram.settings")
django.setup()

import logging

def test_file_logging():
    """Test that logs are properly written to files"""

    print("Testing File Logging Configuration")
    print("=" * 50)

    # Test different loggers with various levels
    print("\nTesting main application logger:")
    agritram_logger = logging.getLogger("agritram")
    agritram_logger.debug("DEBUG: This should appear in console only")
    agritram_logger.info("INFO: This should appear in console AND files")
    agritram_logger.warning("WARNING: This should appear in console AND files")
    agritram_logger.error("ERROR: This should appear in console AND files")

    print("\nTesting user service logger:")
    user_logger = logging.getLogger("user.services.registration_orchestrator")
    user_logger.debug("DEBUG: User service debug (console only)")
    user_logger.info("INFO: User service info (console + files)", extra={
        "operation": "TEST_LOGGING",
        "user_id": 123,
        "email": "<EMAIL>"
    })
    user_logger.warning("WARNING: User service warning (console + files)")
    user_logger.error("ERROR: User service error (console + files)")

    print("\nTesting business app loggers:")
    news_logger = logging.getLogger("news")
    transactions_logger = logging.getLogger("transactions")
    crops_logger = logging.getLogger("crops")
    
    news_logger.info("INFO: Article published (console + files)")
    transactions_logger.warning("WARNING: Payment delayed (console + files)")
    crops_logger.error("ERROR: Harvest data error (console + files)")

    print("\nTesting security loggers:")
    security_logger = logging.getLogger("security")
    oauth2_logger = logging.getLogger("oauth2_auth")
    
    security_logger.warning("WARNING: Suspicious activity detected (console + files)")
    oauth2_logger.info("INFO: Token refreshed (console + files)")

    print("\n" + "=" * 50)
    print("File logging test completed!")
    print("\nExpected Results:")
    print("- Console: All log levels visible (DEBUG, INFO, WARNING, ERROR)")
    print("- Files: Only INFO, WARNING, ERROR (no DEBUG)")
    print("\nCheck these files:")
    print("- logs/all.log: All logs except DEBUG")
    print("- logs/api.log: API and app logs (INFO+)")
    print("- logs/security.log: Security events (WARNING+)")
    print("- logs/errors.log: Error level and above")

if __name__ == "__main__":
    test_file_logging()
