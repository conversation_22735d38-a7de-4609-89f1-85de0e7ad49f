#!/usr/bin/env python3
"""
Test script to verify enhanced logging configuration:
1. Full line colors in console
2. All logs (except DEBUG) written to files
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django with DEBUG=True to enable colorlog and debug logs
os.environ["DEBUG"] = "True"
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agritram.settings")
django.setup()

import logging

def test_enhanced_logging():
    """Test the enhanced logging configuration"""

    print("🎨 Testing Enhanced Logging Configuration")
    print("=" * 60)
    print("🔍 Features being tested:")
    print("   1. Full line colors in console output")
    print("   2. All logs (except DEBUG) written to files")
    print("   3. DEBUG logs only in console (not in files)")
    print("=" * 60)

    # Test different loggers with various levels
    print("\n📝 Testing main application logger:")
    agritram_logger = logging.getLogger("agritram")
    agritram_logger.debug("🔵 DEBUG: This should appear in console only (not in files)")
    agritram_logger.info("🟢 INFO: This should appear in console AND files")
    agritram_logger.warning("🟡 WARNING: This should appear in console AND files")
    agritram_logger.error("🔴 ERROR: This should appear in console AND files")

    print("\n👤 Testing user service logger:")
    user_logger = logging.getLogger("user.services.registration_orchestrator")
    user_logger.debug("🔵 DEBUG: User service debug (console only)")
    user_logger.info("🟢 INFO: User service info (console + files)", extra={
        "operation": "TEST_LOGGING",
        "user_id": 123,
        "email": "<EMAIL>"
    })
    user_logger.warning("🟡 WARNING: User service warning (console + files)")
    user_logger.error("🔴 ERROR: User service error (console + files)")

    print("\n🌾 Testing business app loggers:")
    news_logger = logging.getLogger("news")
    transactions_logger = logging.getLogger("transactions")
    crops_logger = logging.getLogger("crops")
    
    news_logger.info("🟢 NEWS: Article published (console + files)")
    transactions_logger.warning("🟡 TRANSACTIONS: Payment delayed (console + files)")
    crops_logger.error("🔴 CROPS: Harvest data error (console + files)")

    print("\n🔒 Testing security loggers:")
    security_logger = logging.getLogger("security")
    oauth2_logger = logging.getLogger("oauth2_auth")
    
    security_logger.warning("🟡 SECURITY: Suspicious activity detected (console + files)")
    oauth2_logger.info("🟢 OAUTH2: Token refreshed (console + files)")

    print("\n🔧 Testing third-party loggers:")
    rest_framework_logger = logging.getLogger("rest_framework")
    corsheaders_logger = logging.getLogger("corsheaders")
    
    rest_framework_logger.warning("🟡 DRF: API rate limit warning (console + files)")
    corsheaders_logger.warning("🟡 CORS: Origin not allowed (console + files)")

    print("\n" + "=" * 60)
    print("✅ Enhanced logging test completed!")
    print("\n📊 Expected Results:")
    print("🎨 Console Output:")
    print("   - Full line colors for each log level")
    print("   - All log levels visible (DEBUG, INFO, WARNING, ERROR)")
    print("\n📁 File Output:")
    print("   - logs/all.log: All logs except DEBUG")
    print("   - logs/api.log: API and app logs (INFO+)")
    print("   - logs/security.log: Security events (WARNING+)")
    print("   - logs/errors.log: Error level and above")
    print("\n🔍 Verification:")
    print("   1. Check console for full line colors")
    print("   2. Check logs/all.log for INFO/WARNING/ERROR (no DEBUG)")
    print("   3. DEBUG logs should only appear in console")

if __name__ == "__main__":
    test_enhanced_logging()
