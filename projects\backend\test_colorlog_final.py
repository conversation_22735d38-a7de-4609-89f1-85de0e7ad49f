#!/usr/bin/env python3
"""
Final test to demonstrate:
1. Full line colors in console
2. All logs (except DEBUG) written to files
"""

import os
import sys
import django
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Setup Django with DEBUG=True to enable colorlog
os.environ["DEBUG"] = "True"
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "agritram.settings")
django.setup()

import logging

def test_final_colorlog():
    """Final test of colorlog with full line colors"""

    print("Final Colorlog Test - Full Line Colors")
    print("=" * 50)

    # Test main logger
    logger = logging.getLogger("agritram")
    
    print("\nTesting full line colors:")
    logger.debug("DEBUG message - console only, not in files")
    logger.info("INFO message - console with full line color + files")
    logger.warning("WARNING message - console with full line color + files")
    logger.error("ERROR message - console with full line color + files")
    logger.critical("CRITICAL message - console with full line color + files")

    print("\nTesting user service with structured data:")
    user_logger = logging.getLogger("user.services.registration_orchestrator")
    user_logger.info("User registration completed", extra={
        "operation": "REGISTRATION_SUCCESS",
        "user_id": 12345,
        "email": "<EMAIL>",
        "device_id": "device_abc123"
    })

    print("\n" + "=" * 50)
    print("Configuration Summary:")
    print("1. Full line colors: Each log level colors the entire line")
    print("2. File logging: INFO+ levels written to files, DEBUG excluded")
    print("3. Multiple handlers: Console + multiple file outputs")
    print("4. Correlation ID: Unique request tracking across all logs")
    print("5. UTF-8 encoding: Proper Unicode support in files")

if __name__ == "__main__":
    test_final_colorlog()
